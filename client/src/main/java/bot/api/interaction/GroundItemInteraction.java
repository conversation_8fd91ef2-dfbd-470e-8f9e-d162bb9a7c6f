package bot.api.interaction;

import bot.util.entity.EntityUtils;
import bot.util.game.entity.GroundItem;
import bot.util.game.world.Tile;

import bot.util.entity.ScreenUtils;
import bot.util.logging.Logger;
import bot.util.logging.LoggerFactory;
import rt4.Mouse;
import rt4.Cross;

import java.util.Arrays;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.locks.ReentrantLock;

/**
 * Class for interacting with ground items
 */
public class GroundItemInteraction {
    private static final Logger logger = LoggerFactory.getLogger("GroundItemInteraction");

    // Lock to prevent concurrent ground item interactions
    private static final ReentrantLock interactionLock = new ReentrantLock();

    // Default timeout for ground item interactions in milliseconds
    public static final long DEFAULT_INTERACTION_TIMEOUT = 5000;

    /**
     * Interacts with the nearest ground item of the specified ID using default timeout
     *
     * @param id The ID of the ground item to interact with
     * @param option The interaction option to select (e.g. "Take", "Examine", etc.)
     * @return true if successful, false otherwise
     * @throws InterruptedException if the thread is interrupted during the operation
     * @throws TimeoutException if the operation times out
     */
    public static boolean interactWithNearestGroundItem(int id, String option) throws InterruptedException, TimeoutException {
        return interactWithNearestGroundItem(id, option, DEFAULT_INTERACTION_TIMEOUT);
    }

    /**
     * Interacts with the nearest ground item of the specified ID with custom timeout
     *
     * @param id The ID of the ground item to interact with
     * @param option The interaction option to select (e.g. "Take", "Examine", etc.)
     * @param timeoutMs The timeout in milliseconds
     * @return true if successful, false otherwise
     * @throws InterruptedException if the thread is interrupted during the operation
     * @throws TimeoutException if the operation times out
     * @throws IllegalArgumentException if id is negative or timeoutMs is negative
     */
    public static boolean interactWithNearestGroundItem(int id, String option, long timeoutMs) throws InterruptedException, TimeoutException {
        // Validate parameters
        if (id < 0) {
            throw new IllegalArgumentException("Ground item ID cannot be negative: " + id);
        }
        if (timeoutMs < 0) {
            throw new IllegalArgumentException("Timeout cannot be negative: " + timeoutMs);
        }

        // Find the nearest ground item with the given ID
        GroundItem item = EntityUtils.findNearestGroundItem(id);
        if (item == null) {
            logger.warning("No ground item found with ID: " + id);
            return false;
        }

        return interactWithGroundItem(item, option, timeoutMs);
    }

    /**
     * Interacts with the specified ground item using default timeout
     *
     * @param item The ground item to interact with
     * @param option The interaction option to select (e.g. "Take", "Examine", etc.)
     * @return true if successful, false otherwise
     * @throws InterruptedException if the thread is interrupted during the operation
     * @throws TimeoutException if the operation times out
     */
    public static boolean interactWithGroundItem(GroundItem item, String option) throws InterruptedException, TimeoutException {
        return interactWithGroundItem(item, option, DEFAULT_INTERACTION_TIMEOUT);
    }

    /**
     * Interacts with the specified ground item with custom timeout
     *
     * @param item The ground item to interact with
     * @param option The interaction option to select (e.g. "Take", "Examine", etc.)
     * @param timeoutMs The timeout in milliseconds
     * @return true if successful, false otherwise
     * @throws InterruptedException if the thread is interrupted during the operation
     * @throws TimeoutException if the operation times out
     * @throws IllegalArgumentException if item is null or timeoutMs is negative
     */
    public static boolean interactWithGroundItem(GroundItem item, String option, long timeoutMs) throws InterruptedException, TimeoutException {
        // Validate parameters
        if (item == null || item.getItem() == null || item.getItem().getId() < 0) {
            throw new IllegalArgumentException("Ground item cannot be null or invalid");
        }
        if (timeoutMs < 0) {
            throw new IllegalArgumentException("Timeout cannot be negative: " + timeoutMs);
        }

        // Acquire the interaction lock to prevent concurrent ground item interactions
        if (!interactionLock.tryLock()) {
            logger.warning("Another ground item interaction is in progress, waiting for lock...");
            interactionLock.lock();
        }

        try {

            // Check if the ground item is on screen
            if (!ScreenUtils.isGroundItemOnScreen(item)) {
                // Ground item is not on screen, walk to it first
                Tile itemTile = item.getLocation();
                if (itemTile == null) {
                    logger.warning("Could not get ground item location");
                    return false;
                }

                logger.info("Walking to ground item at " + itemTile);
                // Walk to the ground item using local path with the specified timeout
                if (!WalkInteraction.walkToLocal(itemTile)) {
                    logger.warning("Failed to walk to ground item within timeout: " + timeoutMs + "ms");
                    throw new TimeoutException("Failed to walk to ground item within timeout: " + timeoutMs + "ms");
                }

                // Check again if the ground item is on screen after walking
                if (!ScreenUtils.isGroundItemOnScreen(item)) {
                    logger.warning("Ground item still not on screen after walking");
                    return false;
                }
            }

            // Get the screen coordinates of the ground item
            ScreenUtils.ScreenCoords coords = ScreenUtils.getGroundItemScreenCoords(item);
            if (!coords.isOnScreen) {
                logger.warning("Ground item coordinates are not on screen");
                return false;
            }

            // Set mouse position
            try {
                bot.impl.input.MouseHandler.moveMouse(coords.x, coords.y);
            } catch (Exception e) {
                logger.error("Error moving mouse to ground item: " + e.getMessage(), e);
                return false;
            }

            // Find the option index
            int optionIndex = -1;
            if (option != null) {
                String[] options = getGroundItemOptions(item);
                logger.debug("Available ground item options: " + Arrays.toString(options));

                for (int i = 0; i < options.length; i++) {
                    if (options[i] != null && options[i].equals(option)) {
                        optionIndex = i;
                        break;
                    }
                }

                // If option not found, use first available option
                if (optionIndex == -1) {
                    logger.warning("Option '" + option + "' not found for ground item. Available options: " + Arrays.toString(options));
                    return false;
                }
            }

            try {
                // Interact with the ground item using the specified option
                boolean success = MenuInteraction.interact(option);

                if (success) {
                    // Set the cross for visual feedback
                    Cross.x = Mouse.clickX;
                    Cross.y = Mouse.clickY;
                    Cross.type = 2;
                    Cross.milliseconds = 0;

                    // Send the appropriate packet based on the option index
                    sendGroundItemInteractionPacket(item, optionIndex);

                    logger.info("Successfully interacted with ground item ID: " + item.getItem().getId() +
                               (option != null ? " using option: " + option : ""));
                }

                return success;
            } catch (Exception e) {
                logger.error("Error during ground item interaction: " + e.getMessage(), e);
                return false;
            }
        } finally {
            // Always release the lock
            interactionLock.unlock();
        }
    }

    /**
     * Sends the appropriate packet for ground item interaction
     *
     * @param item The ground item to interact with
     * @param optionIndex The index of the option to select
     */
    private static void sendGroundItemInteractionPacket(GroundItem item, int optionIndex) {
        logger.info("Interacting with ground item: " + item.getItem().getId() + ", option index: " + optionIndex);

        // Get the item location and convert to global coordinates
       Tile location = item.getLocation();
        int globalX = location.getGlobalX();
        int globalY = location.getGlobalY();
        int itemId = item.getItem().getId();

        // Send the appropriate packet based on the option index
        switch(optionIndex + 1) {
            case 1: // First option (usually "Take")
                rt4.Protocol.outboundBuffer.p1isaac(66);
                rt4.Protocol.outboundBuffer.ip2(globalX);
                rt4.Protocol.outboundBuffer.p2(itemId);
                rt4.Protocol.outboundBuffer.ip2add(globalY);
                break;
            case 2: // Second option (usually "Examine")
                rt4.Protocol.outboundBuffer.p1isaac(33);
                rt4.Protocol.outboundBuffer.p2(itemId);
                rt4.Protocol.outboundBuffer.p2(globalX);
                rt4.Protocol.outboundBuffer.ip2(globalY);
                break;
            case 3: // Third option
                rt4.Protocol.outboundBuffer.p1isaac(123);
                rt4.Protocol.outboundBuffer.p2(itemId);
                rt4.Protocol.outboundBuffer.p2add(globalX);
                rt4.Protocol.outboundBuffer.p2add(globalY);
                break;
            case 4: // Fourth option
                rt4.Protocol.outboundBuffer.p1isaac(188);
                rt4.Protocol.outboundBuffer.p2(itemId);
                rt4.Protocol.outboundBuffer.p2(globalX);
                rt4.Protocol.outboundBuffer.p2(globalY);
                break;
            case 5: // Fifth option
                rt4.Protocol.outboundBuffer.p1isaac(72);
                rt4.Protocol.outboundBuffer.p2(itemId);
                rt4.Protocol.outboundBuffer.p2(globalX);
                rt4.Protocol.outboundBuffer.p2add(globalY);
                break;
            default:
                logger.warning("Invalid option index: " + optionIndex);
                break;
        }
    }

    /**
     * Gets the available options for a ground item
     *
     * @param item The ground item to get options for
     * @return Array of option strings
     */
    public static String[] getGroundItemOptions(GroundItem item) {
        if (item == null || item.getItem() == null || item.getItem().getId() < 0) {
            return new String[0];
        }

        // Default options for ground items
        return new String[]{"Take", "Examine"};
    }

    /**
     * Clicks on the nearest ground item of the specified ID
     *
     * @param id The ID of the ground item to click
     * @return true if successful, false otherwise
     * @throws InterruptedException if the thread is interrupted during the operation
     * @throws TimeoutException if the operation times out
     */
    public static boolean clickNearestGroundItem(int id) throws InterruptedException, TimeoutException {
        // Find the nearest ground item with the given ID
        GroundItem item = EntityUtils.findNearestGroundItem(id);
        if (item == null) {
            return false;
        }

        return clickGroundItem(item);
    }

    /**
     * Clicks on the specified ground item using default timeout
     *
     * @param item The ground item to click
     * @return true if successful, false otherwise
     * @throws InterruptedException if the thread is interrupted during the operation
     * @throws TimeoutException if the operation times out
     */
    public static boolean clickGroundItem(GroundItem item) throws InterruptedException, TimeoutException {
        return interactWithGroundItem(item, null, DEFAULT_INTERACTION_TIMEOUT); // null means left click (first option)
    }

    // Screen coordinate methods have been moved to ScreenUtils
}
