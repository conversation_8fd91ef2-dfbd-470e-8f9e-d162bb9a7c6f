package bot.api.interaction;

import bot.impl.input.MouseHandler;
import bot.util.game.entity.Player;
import bot.util.game.world.Tile;
import bot.util.game.entity.Npc;
import bot.util.entity.ScreenUtils;
import bot.util.logging.Logger;
import bot.util.logging.LoggerFactory;
import rt4.Protocol;

import java.util.Arrays;
import java.util.concurrent.TimeoutException;
import java.util.function.Predicate;
import java.util.concurrent.locks.ReentrantLock;

/**
 * Class for interacting with NPCs
 */
public class NPCInteraction {
    private static final Logger logger = LoggerFactory.getLogger("NPCInteraction");

    // Lock to prevent concurrent NPC interactions
    private static final ReentrantLock interactionLock = new ReentrantLock();

    // Default timeout for NPC interactions in milliseconds
    static final long DEFAULT_INTERACTION_TIMEOUT = 5000;

    /**
     * Finds the nearest NPC that matches the given predicate.
     *
     * @param predicate The predicate to match NPCs against
     * @return The nearest matching NPC, or null if none found
     * @throws IllegalArgumentException if predicate is null
     */
    public static Npc findNearestNPC(Predicate<Npc> predicate) {
        if (predicate == null) {
            throw new IllegalArgumentException("Predicate cannot be null");
        }
        try {
            // Get all NPCs
            rt4.Npc[] rtNpcs = rt4.NpcList.npcs;
            if (rtNpcs == null) {
                return null;
            }

            Npc nearest = null;
            double nearestDistance = Double.MAX_VALUE;

            // Get player location
            Tile playerLocation = Player.getLocation();

            // Find the nearest NPC that matches the predicate
            for (int i = 0; i < rt4.NpcList.size; i++) {
                int npcIndex = rt4.NpcList.ids[i];
                rt4.Npc rtNpc = rtNpcs[npcIndex];

                if (rtNpc != null && rtNpc.type != null) {
                    Npc npc = new Npc(rtNpc, npcIndex);

                    if (predicate.test(npc)) {
                        Tile npcLocation = npc.getLocation();
                        if (npcLocation != null) {
                            double distance = playerLocation.distanceTo(npcLocation);
                            if (distance < nearestDistance) {
                                nearestDistance = distance;
                                nearest = npc;
                            }
                        }
                    }
                }
            }

            return nearest;
        } catch (Exception e) {
            logger.error("Error finding nearest NPC: " + e.getMessage());
            return null;
        }
    }

    /**
     * Finds the nearest NPC with the given ID.
     *
     * @param id The ID of the NPC to find
     * @return The nearest matching NPC, or null if none found
     */
    public static Npc findNearestNPC(int id) {
        return findNearestNPC(npc -> npc != null && npc.getId() == id);
    }

    /**
     * Finds the nearest NPC with the given name.
     *
     * @param name The name of the NPC to find
     * @return The nearest matching NPC, or null if none found
     */
    public static Npc findNearestNPC(String name) {
        return findNearestNPC(npc ->
            npc != null &&
            npc.getName() != null &&
            npc.getName().equals(name)
        );
    }

    /**
     * Interacts with the nearest NPC of the specified ID
     *
     * @param id The ID of the NPC to interact with
     * @param option The interaction option to select (e.g. "Talk-to", "Attack", etc.)
     * @return true if successful, false otherwise
     * @throws InterruptedException if the thread is interrupted during the operation
     * @throws TimeoutException if the operation times out
     */
    public static boolean interactWithNearestNPC(int id, String option) throws InterruptedException, TimeoutException {
        return interactWithNearestNPC(id, option, DEFAULT_INTERACTION_TIMEOUT);
    }

    /**
     * Interacts with the nearest NPC of the specified ID with a custom timeout
     *
     * @param id The ID of the NPC to interact with
     * @param option The interaction option to select (e.g. "Talk-to", "Attack", etc.)
     * @param timeoutMs The timeout in milliseconds
     * @return true if successful, false otherwise
     * @throws InterruptedException if the thread is interrupted during the operation
     * @throws TimeoutException if the operation times out
     */
    public static boolean interactWithNearestNPC(int id, String option, long timeoutMs) throws InterruptedException, TimeoutException {
        // Validate parameters
        if (id < 0) {
            throw new IllegalArgumentException("NPC ID cannot be negative: " + id);
        }

        // Find the nearest NPC with the given ID
        Npc npc = findNearestNPC(n -> n != null && n.getId() == id);
        if (npc == null) {
            logger.warning("No NPC found with ID: " + id);
            return false;
        }

        return interactWithNPC(npc, option, timeoutMs);
    }



    /**
     * Interacts with the specified NPC using the default timeout
     *
     * @param npc The NPC to interact with
     * @param option The interaction option to select (e.g. "Talk-to", "Attack", etc.)
     * @return true if successful, false otherwise
     * @throws InterruptedException if the thread is interrupted during the operation
     * @throws TimeoutException if the operation times out
     */
    public static boolean interactWithNPC(Npc npc, String option) throws InterruptedException, TimeoutException {
        return interactWithNPC(npc, option, DEFAULT_INTERACTION_TIMEOUT);
    }

    /**
     * Interacts with the specified NPC with a custom timeout
     *
     * @param npc The NPC to interact with
     * @param option The interaction option to select (e.g. "Talk-to", "Attack", etc.)
     * @param timeoutMs The timeout in milliseconds
     * @return true if successful, false otherwise
     * @throws InterruptedException if the thread is interrupted during the operation
     * @throws TimeoutException if the operation times out
     * @throws IllegalArgumentException if npc is null or timeoutMs is negative
     */
    public static boolean interactWithNPC(Npc npc, String option, long timeoutMs) throws InterruptedException, TimeoutException {
        // Validate parameters
        if (npc == null) {
            throw new IllegalArgumentException("NPC cannot be null");
        }
        if (timeoutMs < 0) {
            throw new IllegalArgumentException("Timeout cannot be negative: " + timeoutMs);
        }

        // Acquire the interaction lock to prevent concurrent NPC interactions
        if (!interactionLock.tryLock()) {
            logger.warning("Another NPC interaction is in progress, waiting for lock...");
            interactionLock.lock();
        }

        try {
            // Get the underlying rt4.Npc
            rt4.Npc rtNpc = npc.getUnderlying();
            if (rtNpc == null || rtNpc.type == null) {
                logger.warning("Invalid underlying NPC");
                return false;
            }

            // Check if the NPC is on screen
            if (!ScreenUtils.isNpcOnScreen(npc)) {
                // NPC is not on screen, walk to it first
                Tile npcTile = npc.getLocation();
                if (npcTile == null) {
                    logger.warning("Could not get NPC location");
                    return false;
                }

                logger.info("Walking to NPC at " + npcTile);
                // Walk to the NPC using local path with the specified timeout
                if (!WalkInteraction.walkToLocal(npcTile)) {
                    logger.warning("Failed to walk to NPC within timeout: " + timeoutMs + "ms");
                    throw new TimeoutException("Failed to walk to NPC within timeout: " + timeoutMs + "ms");
                }

                // Check again if the NPC is on screen after walking
                if (!ScreenUtils.isNpcOnScreen(npc)) {
                    logger.warning("NPC still not on screen after walking");
                    return false;
                }
            }

            // Get the screen coordinates of the NPC
            ScreenUtils.ScreenCoords coords = ScreenUtils.getNpcScreenCoords(npc);
            if (!coords.isOnScreen) {
                logger.warning("NPC coordinates are not on screen");
                return false;
            }

            // Set mouse position
            try {
                MouseHandler.moveMouse(coords.x, coords.y);
            } catch (Exception e) {
                logger.error("Error moving mouse to NPC: " + e.getMessage(), e);
                return false;
            }

            // Find the option index
            int optionIndex = -1;
            if (option != null) {
                String[] options = getNpcOptions(npc);
                logger.debug("Available NPC options: " + Arrays.toString(options));

                for (int i = 0; i < options.length; i++) {
                    if (options[i] != null && options[i].equals(option)) {
                        optionIndex = i;
                        break;
                    }
                }

                // If option not found, use first available option
                if (optionIndex == -1) {
                    logger.warning("Option '" + option + "' not found for NPC. Available options: " + Arrays.toString(options));
                    return false;
                }
            }




            try {
                // Set the cross for visual feedback
                MouseHandler.setCross(coords.x, coords.y);

                // Simulate a mouse click for visual feedback
                MouseHandler.click();

                // Send the appropriate packet based on the option index
                sendNpcInteractionPacket(npc, optionIndex);

                logger.info("Successfully interacted with NPC: " + npc.getName() +
                           (option != null ? " using option: " + option : ""));
                return true;
            } catch (Exception e) {
                logger.error("Error during NPC interaction: " + e.getMessage(), e);
                return false;
            }
        } finally {
            // Always release the lock
            interactionLock.unlock();
        }
    }

    /**
     * Sends the appropriate packet for NPC interaction
     *
     * @param npc The NPC to interact with
     * @param optionIndex The index of the option to select
     * @throws IllegalArgumentException if npc is null or optionIndex is invalid
     */
    private static void sendNpcInteractionPacket(Npc npc, int optionIndex) {
        if (npc == null) {
            throw new IllegalArgumentException("NPC cannot be null");
        }
        if (optionIndex < -1 || optionIndex >= 5) { // -1 is valid for default action
            throw new IllegalArgumentException("Invalid option index: " + optionIndex);
        }
        int npcIndex = npc.getIndex();
        if (npcIndex < 0) {
            throw new IllegalArgumentException("Invalid NPC index: " + npcIndex);
        }

        logger.info("Interacting with NPC " + npc.getName() + " (ID: " + npc.getId() + ") using option index: " + optionIndex);

        try {
            // Send the appropriate packet based on the option index
            switch(optionIndex + 1) {
                case 1:
                    Protocol.outboundBuffer.p1isaac(78);
                    Protocol.outboundBuffer.ip2(npcIndex);
                    break;
                case 2:
                    Protocol.outboundBuffer.p1isaac(3);
                    Protocol.outboundBuffer.ip2add(npcIndex);
                    break;
                case 3:
                    Protocol.outboundBuffer.p1isaac(148);
                    Protocol.outboundBuffer.p2add(npcIndex);
                    break;
                case 4:
                    Protocol.outboundBuffer.p1isaac(30);
                    Protocol.outboundBuffer.p2(npcIndex);
                    break;
                case 5:
                    Protocol.outboundBuffer.p1isaac(218);
                    Protocol.outboundBuffer.ip2(npcIndex);
                    break;
                default:
                    // Use first option as fallback
                    logger.warning("Invalid option index: " + optionIndex + ", using first option as fallback");
                    Protocol.outboundBuffer.p1isaac(78);
                    Protocol.outboundBuffer.ip2(npcIndex);
                    break;
            }
        } catch (Exception e) {
            logger.error("Error sending NPC interaction packet: " + e.getMessage(), e);
            throw new RuntimeException("Failed to send NPC interaction packet", e);
        }
    }

    /**
     * Gets the available options for an NPC
     *
     * @param npc The NPC to get options for
     * @return Array of option strings
     */
    public static String[] getNpcOptions(Npc npc) {
        if (npc == null) {
            return new String[0];
        }

        rt4.Npc rtNpc = npc.getUnderlying();
        if (rtNpc == null || rtNpc.type == null ) {
            return new String[0];
        }

        String[] options = new String[rtNpc.type.ops.length];
        for (int i = 0; i < rtNpc.type.ops.length; i++) {
            if (rtNpc.type.ops[i] != null) {
                options[i] = rtNpc.type.ops[i].toString();
            }
        }

        return options;
    }

    /**
     * Clicks on the nearest NPC of the specified ID using the default timeout
     *
     * @param id The ID of the NPC to click
     * @return true if successful, false otherwise
     * @throws InterruptedException if the thread is interrupted during the operation
     * @throws TimeoutException if the operation times out
     * @throws IllegalArgumentException if id is negative
     */
    public static boolean clickNearestNPC(int id) throws InterruptedException, TimeoutException {
        return clickNearestNPC(id, DEFAULT_INTERACTION_TIMEOUT);
    }

    /**
     * Clicks on the nearest NPC of the specified ID with a custom timeout
     *
     * @param id The ID of the NPC to click
     * @param timeoutMs The timeout in milliseconds
     * @return true if successful, false otherwise
     * @throws InterruptedException if the thread is interrupted during the operation
     * @throws TimeoutException if the operation times out
     * @throws IllegalArgumentException if id is negative or timeoutMs is negative
     */
    public static boolean clickNearestNPC(int id, long timeoutMs) throws InterruptedException, TimeoutException {
        // Validate parameters
        if (id < 0) {
            throw new IllegalArgumentException("NPC ID cannot be negative: " + id);
        }
        if (timeoutMs < 0) {
            throw new IllegalArgumentException("Timeout cannot be negative: " + timeoutMs);
        }

        // Find the nearest NPC with the given ID
        Npc npc = findNearestNPC(n -> n != null && n.getId() == id);
        if (npc == null) {
            logger.warning("No NPC found with ID: " + id);
            return false;
        }

        return clickNPC(npc, timeoutMs);
    }

    /**
     * Clicks on the specified NPC using the default timeout
     *
     * @param npc The NPC to click
     * @return true if successful, false otherwise
     * @throws InterruptedException if the thread is interrupted during the operation
     * @throws TimeoutException if the operation times out
     * @throws IllegalArgumentException if npc is null
     */
    public static boolean clickNPC(Npc npc) throws InterruptedException, TimeoutException {
        return clickNPC(npc, DEFAULT_INTERACTION_TIMEOUT);
    }

    /**
     * Clicks on the specified NPC with a custom timeout
     *
     * @param npc The NPC to click
     * @param timeoutMs The timeout in milliseconds
     * @return true if successful, false otherwise
     * @throws InterruptedException if the thread is interrupted during the operation
     * @throws TimeoutException if the operation times out
     * @throws IllegalArgumentException if npc is null or timeoutMs is negative
     */
    public static boolean clickNPC(Npc npc, long timeoutMs) throws InterruptedException, TimeoutException {
        return interactWithNPC(npc, null, timeoutMs); // null means left click (first option)
    }

    // Screen coordinate methods have been moved to ScreenUtils
}
