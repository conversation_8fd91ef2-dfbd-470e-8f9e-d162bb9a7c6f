package bot.api.interaction;

import bot.util.game.AnimationUtils;
import bot.util.game.world.Tile;
import bot.util.logging.Logger;
import bot.util.logging.LoggerFactory;
import bot.util.game.entity.Player;
import bot.util.error.TimeoutConfig;
import bot.util.error.TimeoutHandler;
import rt4.*;

import java.util.*;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.locks.ReentrantLock;

/**
 * Utility class for walking and movement in the game world.
 * Provides methods for pathfinding, walking to locations, checking reachability,
 * and converting between coordinate systems. This class handles both local movement
 * (within the current scene) and global movement (across the entire game world).
 *
 * Main Methods:
 * - walkToTile() - Basic minimap walking to a single tile
 * - walkPath() - Walking along a predefined path
 * - walkToLocal() - Intelligent walking to nearby entities (NPCs, objects, items)
 * - walkToTileWithPathfinding() - Enhanced walking with A* pathfinding for long distances
 */
public class WalkInteraction {
    private static final Logger logger = LoggerFactory.getLogger("WalkInteraction");

    // Lock to prevent concurrent walking operations
    private static final ReentrantLock walkLock = new ReentrantLock();

    // ===== CORE LOCATION AND MOVEMENT UTILITIES =====

    /**
     * Gets the player's current location
     *
     * @return The player's current location as a Tile
     */
    public static Tile getPlayerLocation() {
        return Player.getLocation();
    }



    /**
     * Checks if a tile is within interaction reach of the player
     * This method determines if the player can interact with objects or entities
     * at the specified tile without needing to move closer. The maximum interaction
     * distance is typically 1.5 tiles in Euclidean distance, and the tile must be
     * on the same plane as the player.
     *
     * @param tile The tile to check for reachability
     * @return true if the player can interact with the tile without moving, false otherwise
     */
    public static boolean isWithinReach(Tile tile) {


        // Get player's current location
        Tile playerLoc = getPlayerLocation();

        // Calculate distance to tile
        int deltaX = playerLoc.getX() - tile.getX();
        int deltaY = playerLoc.getY() - tile.getY();
        double distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

        // Check if the tile is on the same plane
        if (tile.getPlane() != playerLoc.getPlane()) {
            return false;
        }

        // Check if the tile is within interaction distance (typically 1-2 tiles)
        // We'll use a conservative value of 1.5 tiles
        if (distance > 1.5) {
            return false;
        }

        // Check if there's a direct path to the tile
        // This is a simplified check - in a full implementation, we would check for obstacles
        boolean pathExists = checkDirectPath(playerLoc, tile);
        if (!pathExists) {
            return false;
        }

        return true;
    }

    /**
     * Checks if there's a direct path between two tiles
     * This is a simplified implementation that doesn't check for all obstacles
     *
     * @param start The starting tile
     * @param end The ending tile
     * @return true if there's a direct path
     */
    private static boolean checkDirectPath(Tile start, Tile end) {
        // Get collision flags for the current plane
        int plane = start.getPlane();

        // Convert to local coordinates
        int startX = start.getX() - rt4.Camera.originX;
        int startY = start.getY() - rt4.Camera.originZ;
        int endX = end.getX() - rt4.Camera.originX;
        int endY = end.getY() - rt4.Camera.originZ;

        // Check if the tiles are within the scene bounds
        if (startX < 0 || startY < 0 || endX < 0 || endY < 0 ||
                startX >= 104 || startY >= 104 || endX >= 104 || endY >= 104) {
            return false;
        }

        // Check if the end tile is blocked
        int flags = SceneGraph.renderFlags[plane][endX][endY];
        if ((flags & 0x1280100) != 0) { // Check for wall and object blocking
            return false;
        }

        // For a more accurate check, we would trace the path between the tiles
        // and check for obstacles, but this simplified version just checks the end tile

        return true;
    }

    /**
     * Gets the player's current location in global coordinates
     * The game uses two coordinate systems: local (scene-relative) and global (world-relative).
     * This method automatically detects which coordinate system the player's location is in
     * and converts to global coordinates if necessary. Global coordinates are consistent
     * across the entire game world, while local coordinates change as the scene loads.
     *
     * @return The player's global location as a Tile (absolute world position)
     */
    public static Tile getPlayerGlobalLocation() {
        Tile playerLoc = getPlayerLocation();

        // Check if the player's coordinates are local
        boolean playerCoordsAreLocal = playerLoc.getX() < 1000 && playerLoc.getY() < 1000;

        if (playerCoordsAreLocal) {
            // Convert local coordinates to global
            return new Tile(
                    playerLoc.getX() + rt4.Camera.originX,
                    playerLoc.getY() + rt4.Camera.originZ,
                    playerLoc.getPlane()
            );
        } else {
            // Already in global coordinates
            return playerLoc;
        }
    }

    /**
     * Checks if the player is moving
     *
     * @return true if the player is moving
     */
    public static boolean isMoving() {
        return AnimationUtils.isMoving();
    }

    // ===== CORE WALKING METHODS =====

    /**
     * Clicks on the minimap to walk to a specific tile
     * This method handles the conversion between global and local coordinates,
     * calculates the correct position on the minimap, and simulates a mouse click
     * to initiate walking. It will wait for the player to start moving and can
     * handle interrupted threads.
     *
     * @param tile The destination tile to walk to (in global coordinates)
     * @return true if the walking action was successfully initiated, false otherwise
     * @throws InterruptedException if the thread is interrupted while waiting
     */
    public static boolean walkToTile(Tile tile) throws InterruptedException {


        // Check if thread has been interrupted
        if (Thread.currentThread().isInterrupted()) {
            throw new InterruptedException("Thread interrupted before walking to tile");
        }

        // Check if the tile is on the same plane
        if (tile.getPlane() != rt4.Player.plane) {
            return false;
        }

        // Get player's current location in tile coordinates
        Tile playerLoc = getPlayerLocation();

        // Check if player coordinates are local (scene-relative) or global
        boolean playerCoordsAreLocal = playerLoc.getX() < 1000 && playerLoc.getY() < 1000;
        int adjustedTargetX = tile.getX();
        int adjustedTargetY = tile.getY();

        if (playerCoordsAreLocal) {
            // Convert global target coordinates to local coordinates
            adjustedTargetX = tile.getX() - rt4.Camera.originX;
            adjustedTargetY = tile.getY() - rt4.Camera.originZ;
        } else {

        }

        // Calculate the difference and distance
        int diffX = adjustedTargetX - playerLoc.getX();
        int diffY = adjustedTargetY - playerLoc.getY();
        double distance = Math.sqrt(diffX * diffX + diffY * diffY);


        // If we're already at the target tile, return success
        if (distance < 1) {
            return true;
        }

        // Get the target tile coordinates
        int targetX = tile.getX();
        int targetY = tile.getY();

        // Convert to local coordinates for pathfinding
        int localX = playerCoordsAreLocal ? adjustedTargetX : targetX - rt4.Camera.originX;
        int localY = playerCoordsAreLocal ? adjustedTargetY : targetY - rt4.Camera.originZ;


        // Calculate the difference and distance to target
        diffX = playerCoordsAreLocal ? (localX - playerLoc.getX()) : (targetX - playerLoc.getX());
        diffY = playerCoordsAreLocal ? (localY - playerLoc.getY()) : (targetY - playerLoc.getY());
        distance = Math.sqrt(diffX * diffX + diffY * diffY);


        // Minimap constants
        int minimapCenterX = 630;
        int minimapCenterY = 85;
        float minimapRadius = 76.5f/2;

        // Initialize minimap coordinates to center
        int minimapX = minimapCenterX;
        int minimapY = minimapCenterY;

        // Calculate minimap click position if not already at destination
        if (distance > 1) {
            // Calculate flag screen position using game's formula
            int flagScreenX = localX * 4 + 2 - rt4.PlayerList.self.xFine / 32;
            int flagScreenY = localY * 4 + 2 - rt4.PlayerList.self.zFine / 32;

            // Get camera rotation values
            int cameraYaw = (rt4.MiniMap.anInt1814 + (int) rt4.Camera.yawTarget) & 0x7FF;
            int zoomFactor = rt4.MiniMap.anInt4130 + 256;

            // Apply rotation and scaling
            int sinYaw = rt4.MathUtils.sin[cameraYaw];
            int cosYaw = rt4.MathUtils.cos[cameraYaw];
            int scaledSin = sinYaw * 256 / zoomFactor;
            int scaledCos = cosYaw * 256 / zoomFactor;
            int rotatedX = (flagScreenX * scaledCos + flagScreenY * scaledSin) >> 16;
            int rotatedY = (flagScreenY * scaledCos - flagScreenX * scaledSin) >> 16;

            // Calculate final screen coordinates
            minimapX = minimapCenterX + rotatedX;
            minimapY = minimapCenterY - rotatedY; // Y is inverted


        }

        // Ensure coordinates are within minimap bounds
        minimapX = Math.max(minimapCenterX - (int)minimapRadius, Math.min(minimapX, minimapCenterX + (int)minimapRadius));
        minimapY = Math.max(minimapCenterY - (int)minimapRadius, Math.min(minimapY, minimapCenterY + (int)minimapRadius));


        // Move mouse to calculated position using MouseInteractionUtil
        bot.impl.input.MouseHandler.moveMouse(minimapX, minimapY);
        long startTime = System.currentTimeMillis();

        try {
            // Check if thread has been interrupted
            if (Thread.currentThread().isInterrupted()) {

                throw new InterruptedException("Thread interrupted before mouse click");
            }

            // Store the current mouse position
            int currentX = Mouse.currentMouseX;
            int currentY = Mouse.currentMouseY;

            // Simulate mouse click
            MenuInteraction.leftClick();

            // Ensure Cross is set to the current mouse position
            Cross.x = currentX;
            Cross.y = currentY;
            Cross.type = 2;
            Cross.milliseconds = 0;

            // Wait for click to be processed
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {

                throw e;
            }

            // Find path to destination using game's PathFinder
            boolean pathFound = rt4.PathFinder.findPath(
                    rt4.PlayerList.self.movementQueueZ[0], // Start Z
                    0, // Type 0
                    0, // Size 0
                    true, // Run
                    0, // Unused
                    localX, // Destination X (local)
                    0, // Unused
                    0, // Unused
                    1, // Mode 1 (minimap walking)
                    localY, // Destination Y (local)
                    rt4.PlayerList.self.movementQueueX[0] // Start X
            );



            // Check if thread has been interrupted
            if (Thread.currentThread().isInterrupted()) {

                throw new InterruptedException("Thread interrupted before sending walk packet");
            }

            if (pathFound) {
                // Set destination flag right before sending packet
                rt4.LoginManager.mapFlagX = localX;
                rt4.LoginManager.mapFlagZ = localY;

                // Send walk packet with all required parameters
                Protocol.outboundBuffer.p1(InterfaceList.anInt5);
                Protocol.outboundBuffer.p1(MiniMenu.anInt2878);
                Protocol.outboundBuffer.p2((int) rt4.Camera.yawTarget);
                Protocol.outboundBuffer.p1(57);
                Protocol.outboundBuffer.p1(MiniMap.anInt1814);
                Protocol.outboundBuffer.p1(MiniMap.anInt4130);
                Protocol.outboundBuffer.p1(89);
                Protocol.outboundBuffer.p2(PlayerList.self.xFine);
                Protocol.outboundBuffer.p2(PlayerList.self.zFine);
                Protocol.outboundBuffer.p1(PathFinder.anInt4364);
                Protocol.outboundBuffer.p1(63);

                // Send the packet
                rt4.Protocol.socket.write(rt4.Protocol.outboundBuffer.data, rt4.Protocol.outboundBuffer.offset);
                return true;
            } else {
                return false;
            }
        } catch (InterruptedException e) {

            throw e; // Re-throw the InterruptedException to properly stop the script
        } catch (Exception e) {

            return false;
        }
    }

    // ===== PATH WALKING METHODS =====

    /**
     * Walks a predefined path of tiles
     * This method intelligently navigates a series of waypoints by finding the closest
     * point in the path to start from, then walking from waypoint to waypoint. It handles
     * timeout conditions, thread interruptions, and automatically adjusts if the player
     * is already close to a waypoint.
     *
     * @param path An array of Tiles defining the path to walk (in global coordinates)
     * @return true if the entire path was successfully walked, false if there was an error
     * @throws InterruptedException if the thread is interrupted during walking
     * @throws TimeoutException if the operation times out
     * @throws IllegalArgumentException if path is null or empty
     */
    public static boolean walkPath(Tile[] path) throws InterruptedException, TimeoutException {
        return walkPath(path, TimeoutHandler.getDefaultConfig());
    }

    /**
     * Walks a predefined path of tiles using a specific TimeoutConfig
     * This method intelligently navigates a series of waypoints by finding the closest
     * point in the path to start from, then walking from waypoint to waypoint. It handles
     * timeout conditions, thread interruptions, and automatically adjusts if the player
     * is already close to a waypoint.
     *
     * @param path An array of Tiles defining the path to walk (in global coordinates)
     * @param config The TimeoutConfig to use for timeout values
     * @return true if the entire path was successfully walked, false if there was an error
     * @throws InterruptedException if the thread is interrupted during walking
     * @throws TimeoutException if the operation times out
     * @throws IllegalArgumentException if path is null or empty, or config is null
     */
    public static boolean walkPath(Tile[] path, TimeoutConfig config) throws InterruptedException, TimeoutException {
        if (config == null) {
            throw new IllegalArgumentException("TimeoutConfig cannot be null");
        }
        return walkPath(path, config.getWalkTimeout());
    }

    /**
     * Walks a predefined path of tiles
     * This method intelligently navigates a series of waypoints by finding the closest
     * point in the path to start from, then walking from waypoint to waypoint. It handles
     * timeout conditions, thread interruptions, and automatically adjusts if the player
     * is already close to a waypoint.
     *
     * @param path An array of Tiles defining the path to walk (in global coordinates)
     * @param timeout The maximum time to wait for reaching each waypoint (in milliseconds)
     * @return true if the entire path was successfully walked, false if there was an error
     * @throws InterruptedException if the thread is interrupted during walking
     * @throws TimeoutException if the operation times out
     * @throws IllegalArgumentException if path is null or empty, or timeout is negative
     */
    public static boolean walkPath(Tile[] path, long timeout) throws InterruptedException, TimeoutException {


        // Validate input
        if (path == null || path.length == 0) {
            throw new IllegalArgumentException("Cannot walk an empty path");
        }
        if (timeout < 0) {
            throw new IllegalArgumentException("Timeout cannot be negative: " + timeout);
        }

        // Acquire the walk lock to prevent concurrent walking operations
        if (!walkLock.tryLock()) {
            logger.warning("Another walking operation is in progress, waiting for lock...");
            walkLock.lock();
        }

        try {

            // Check if thread has been interrupted
            if (Thread.currentThread().isInterrupted()) {

                throw new InterruptedException("Thread interrupted before starting path");
            }

            // Find the closest tile in the path to start from
            // Make sure we're using global coordinates for comparison
            Tile playerLoc = getPlayerGlobalLocation();
            int closestIndex = 0;
            double closestDistance = Double.MAX_VALUE;

            // Log player location for debugging
            logger.info("Player global location: " + playerLoc);

            // Ensure all path tiles are in global coordinates
            Tile[] globalPath = new Tile[path.length];
            for (int i = 0; i < path.length; i++) {
                // Check if the path tile is in local coordinates
                if (path[i].getX() < 1000 && path[i].getY() < 1000) {
                    // Convert to global coordinates
                    globalPath[i] = path[i].asGlobal();
                } else {
                    // Already in global coordinates
                    globalPath[i] = path[i];
                }
            }

            // Log the generated path length once
            logger.info("Generated path with " + globalPath.length + " tiles");

            // Find the closest tile in the global path
            for (int i = 0; i < globalPath.length; i++) {
                double distance = playerLoc.distanceTo(globalPath[i]);

                if (distance < closestDistance) {
                    closestDistance = distance;
                    closestIndex = i;
                }
            }

            // Start from the closest waypoint
            int currentIndex = closestIndex;



            // If we're very close to the closest waypoint, move to the next one
            if (closestDistance < 3 && currentIndex < path.length - 1) {
                currentIndex++;

            }



            // Loop through the path starting from the current index
            for (int i = currentIndex; i < path.length; i++) {
                // Check if the script is paused

                // Check if thread has been interrupted
                if (Thread.currentThread().isInterrupted()) {
                    throw new InterruptedException("Thread interrupted during path walking");
                }

                // Get the current waypoint
                Tile currentWaypoint = path[i];

                // Get player location in global coordinates
                playerLoc = getPlayerGlobalLocation();

                // Ensure waypoint is in global coordinates
                Tile globalWaypoint = currentWaypoint;
                if (currentWaypoint.getX() < 1000 && currentWaypoint.getY() < 1000) {
                    globalWaypoint = currentWaypoint.asGlobal();
                }

                // Calculate distance to waypoint using the Tile.distanceTo method
                double distance = playerLoc.distanceTo(globalWaypoint);
                logger.info("Distance to current waypoint: " + distance);

                // Only walk to tiles that are within reasonable distance
                if (distance < 20) {

                    // Walk to the current waypoint
                    boolean walkResult = walkToTile(currentWaypoint);

                    if (!walkResult) {
                        return false;
                    }

                    // Wait until we're close to the destination or movement stops
                    boolean reachedWaypoint;
                    try {
                        reachedWaypoint = waitUntilCloseToDestination(currentWaypoint, 4, timeout);
                    } catch (TimeoutException e) {
                        logger.warning("Timeout while waiting for waypoint: " + e.getMessage());
                        throw e; // Propagate the timeout exception
                    }

                    if (!reachedWaypoint) {
                        // Check if the script is paused

                        // Check if we were interrupted
                        if (Thread.currentThread().isInterrupted()) {
                            throw new InterruptedException("Thread interrupted while waiting for waypoint");
                        }

                        // Continue anyway, as we might be close enough for the next waypoint
                    }

                    // Check if we've reached the end of the path
                    if (i == path.length - 1) {
                        return true;
                    }
                }
            }

            // If we've gone through all waypoints, we've completed the path
            return true;
        } finally {
            // Always release the lock
            walkLock.unlock();
        }
    }

    // ===== PATH AND WALKING UTILITIES =====

    /**
     * Reverses an array of Tiles to create a return path
     * This utility method takes a path and creates a new array with the tiles in reverse order,
     * which is useful for creating return journeys. For example, if you have a path from
     * a bank to a mining site, you can reverse it to get a path from the mining site back
     * to the bank.
     *
     * @param path The original path to reverse
     * @return A new array with the tiles in reverse order, or the original array if it's null or has 0-1 elements
     */
    public static Tile[] reversePath(Tile[] path) {
        if (path == null || path.length <= 1) {
            return path; // Nothing to reverse
        }

        Tile[] reversedPath = new Tile[path.length];
        for (int i = 0; i < path.length; i++) {
            reversedPath[i] = path[path.length - 1 - i];
        }

        return reversedPath;
    }

    // ===== LOCAL WALKING METHODS (for NPCs, Objects, Items) =====

    /**
     * Walks to a nearby destination using intelligent pathfinding
     * This method is optimized for walking to entities like NPCs, objects, or ground items
     * that are not currently visible on screen. It uses A* pathfinding for better navigation
     * around obstacles and handles both short and medium distance walking efficiently.
     *
     * @param destination The destination tile to walk to (in global coordinates)
     * @return true if the destination was successfully reached, false otherwise
     * @throws InterruptedException if the thread is interrupted while waiting
     * @throws TimeoutException if the operation times out
     * @throws IllegalArgumentException if destination is null
     */
    public static boolean walkToLocal(Tile destination) throws InterruptedException, TimeoutException {
        return walkToLocal(destination, TimeoutHandler.getDefaultConfig());
    }

    /**
     * Walks to a nearby destination using the minimap with a specific TimeoutConfig
     * This method is optimized for short-distance walking within the current scene.
     * It attempts to walk directly to the destination and then waits until the player
     * either reaches the destination or the timeout expires. This is particularly useful
     * for walking to nearby entities like NPCs, objects, or ground items that are not
     * currently visible on screen.
     *
     * @param destination The destination tile to walk to (in global coordinates)
     * @param config The TimeoutConfig to use for timeout values
     * @return true if the destination was successfully reached, false otherwise
     * @throws InterruptedException if the thread is interrupted while waiting
     * @throws TimeoutException if the operation times out
     * @throws IllegalArgumentException if destination is null or config is null
     */
    public static boolean walkToLocal(Tile destination, TimeoutConfig config) throws InterruptedException, TimeoutException {
        if (config == null) {
            throw new IllegalArgumentException("TimeoutConfig cannot be null");
        }
        return walkToLocal(destination, config.getWalkTimeout());
    }

    /**
     * Walks to a nearby destination using intelligent pathfinding
     * This method is optimized for walking to entities like NPCs, objects, or ground items
     * that are not currently visible on screen. It uses A* pathfinding for better navigation
     * around obstacles and handles both short and medium distance walking efficiently.
     *
     * @param destination The destination tile to walk to (in global coordinates)
     * @param timeout The maximum time to wait for reaching the destination (in milliseconds)
     * @return true if the destination was successfully reached, false otherwise
     * @throws InterruptedException if the thread is interrupted while waiting
     * @throws TimeoutException if the operation times out
     * @throws IllegalArgumentException if destination is null or timeout is negative
     */
    public static boolean walkToLocal(Tile destination, long timeout) throws InterruptedException, TimeoutException {
        // Validate parameters
        if (destination == null) {
            throw new IllegalArgumentException("Destination cannot be null");
        }
        if (timeout < 0) {
            throw new IllegalArgumentException("Timeout cannot be negative: " + timeout);
        }

        // Acquire the walk lock to prevent concurrent walking operations
        if (!walkLock.tryLock()) {
            logger.warning("Another walking operation is in progress, waiting for lock...");
            walkLock.lock();
        }

        try {
            // Use pathfinding for better navigation
            Tile start = getPlayerGlobalLocation();
            if (start == null) {
                logger.warning("Cannot get player global location for walkToLocal");
                return false;
            }

            // Ensure destination is in global coordinates
            Tile globalDestination = destination;
            if (destination.getX() < 1000 && destination.getY() < 1000) {
                globalDestination = destination.asGlobal();
            }

            double distance = start.distanceTo(globalDestination);
            logger.info("Walking to local destination at distance: " + String.format("%.1f", distance) + " tiles");

            // For very short distances, use direct walking
            if (distance < 5) {
                boolean walkResult = walkToTile(globalDestination);
                if (!walkResult) {
                    return false;
                }
            } else {
                // For medium distances, use A* pathfinding for better navigation
                List<Tile> path = findAStarPath(start, globalDestination, Math.min(50, (int)distance + 10));

                if (path == null || path.isEmpty()) {
                    logger.info("A* pathfinding failed, falling back to direct walk");
                    boolean walkResult = walkToTile(globalDestination);
                    if (!walkResult) {
                        return false;
                    }
                } else {
                    // Optimize path for minimap walking
                    List<Tile> optimizedPath = optimizePathForMinimap(path);
                    logger.info("Using A* path with " + optimizedPath.size() + " waypoints");

                    // Walk the optimized path
                    boolean pathResult = walkPath(optimizedPath.toArray(new Tile[0]), timeout);
                    if (!pathResult) {
                        return false;
                    }
                }
            }

            // Wait until we're close to the destination or timeout
            try {
                return waitUntilCloseToDestination(globalDestination, 4, timeout);
            } catch (TimeoutException e) {
                logger.warning("Timeout while waiting to reach destination: " + e.getMessage());
                throw e; // Propagate the timeout exception to be handled by the caller
            }
        } finally {
            // Always release the lock
            walkLock.unlock();
        }
    }





    /**
     * Waits until the player is within a certain distance of the destination or until timeout
     * This method continuously checks the player's position and compares it to the destination
     * tile. It returns true when the player is within the specified maximum distance of the
     * destination, or false if the timeout expires before reaching the destination. The method
     * includes an initial delay to allow the player to start moving and handles thread
     * interruptions properly.
     *
     * @param destination The destination tile to check distance to (in global coordinates)
     * @param maxDistance The maximum distance in tiles to consider "close enough" to the destination
     * @return true if the player reached the destination within the timeout, false if the timeout expired
     * @throws InterruptedException if the thread is interrupted while waiting
     * @throws TimeoutException if the operation times out
     * @throws IllegalArgumentException if destination is null or maxDistance is negative
     */
    public static boolean waitUntilCloseToDestination(Tile destination, double maxDistance) throws InterruptedException, TimeoutException {
        return waitUntilCloseToDestination(destination, maxDistance, TimeoutHandler.getDefaultConfig());
    }

    /**
     * Waits until the player is within a certain distance of the destination or until timeout
     * This method continuously checks the player's position and compares it to the destination
     * tile. It returns true when the player is within the specified maximum distance of the
     * destination, or false if the timeout expires before reaching the destination. The method
     * includes an initial delay to allow the player to start moving and handles thread
     * interruptions properly.
     *
     * @param destination The destination tile to check distance to (in global coordinates)
     * @param maxDistance The maximum distance in tiles to consider "close enough" to the destination
     * @param config The TimeoutConfig to use for timeout values
     * @return true if the player reached the destination within the timeout, false if the timeout expired
     * @throws InterruptedException if the thread is interrupted while waiting
     * @throws TimeoutException if the operation times out
     * @throws IllegalArgumentException if destination is null, maxDistance is negative, or config is null
     */
    public static boolean waitUntilCloseToDestination(Tile destination, double maxDistance, TimeoutConfig config) throws InterruptedException, TimeoutException {
        if (config == null) {
            throw new IllegalArgumentException("TimeoutConfig cannot be null");
        }
        return waitUntilCloseToDestination(destination, maxDistance, config.getWalkTimeout());
    }

    /**
     * Waits until the player is within a certain distance of the destination or until timeout
     * This method continuously checks the player's position and compares it to the destination
     * tile. It returns true when the player is within the specified maximum distance of the
     * destination, or false if the timeout expires before reaching the destination. The method
     * includes an initial delay to allow the player to start moving and handles thread
     * interruptions properly.
     *
     * @param destination The destination tile to check distance to (in global coordinates)
     * @param maxDistance The maximum distance in tiles to consider "close enough" to the destination
     * @param timeout The maximum time to wait in milliseconds before giving up
     * @return true if the player reached the destination within the timeout, false if the timeout expired
     * @throws InterruptedException if the thread is interrupted while waiting
     * @throws TimeoutException if the operation times out
     * @throws IllegalArgumentException if destination is null, maxDistance is negative, or timeout is negative
     */
    public static boolean waitUntilCloseToDestination(Tile destination, double maxDistance, long timeout) throws InterruptedException, TimeoutException {
        // Validate parameters
        if (destination == null) {
            throw new IllegalArgumentException("Destination cannot be null");
        }
        if (maxDistance < 0) {
            throw new IllegalArgumentException("Max distance cannot be negative: " + maxDistance);
        }
        if (timeout < 0) {
            throw new IllegalArgumentException("Timeout cannot be negative: " + timeout);
        }

        // Check if the script is paused
        long startTime = System.currentTimeMillis();
        long endTime = startTime + timeout;

        // Check if thread has been interrupted before starting
        if (Thread.currentThread().isInterrupted()) {
            throw new InterruptedException("Thread interrupted before waiting for destination");
        }

        // Initial sleep to allow the player to start moving
        try {
            Thread.sleep(500);
        } catch (InterruptedException e) {
            throw e; // Re-throw to properly stop the script
        }

        // Wait until we're close enough to the destination or timeout
        while (System.currentTimeMillis() < endTime) {
            // Check if the script is paused

            // Check if thread has been interrupted (check frequently)
            if (Thread.currentThread().isInterrupted()) {
                throw new InterruptedException("Thread interrupted while waiting for destination");
            }

            // Get player location in global coordinates
            Tile playerLoc = getPlayerGlobalLocation();

            // Ensure destination is in global coordinates
            Tile globalDestination = destination;
            if (destination.getX() < 1000 && destination.getY() < 1000) {
                globalDestination = destination.asGlobal();
            }

            // Calculate distance to destination using the Tile.distanceTo method
            double distance = playerLoc.distanceTo(globalDestination);
            // Removed debug spam - only log when we reach destination

            // If we're close enough to the destination, return success
            if (distance <= maxDistance) {
                logger.info("Successfully reached destination (distance: " + String.format("%.1f", distance) + " tiles)");
                return true;
            }

            // If the player has stopped moving and we're not at the destination yet,
            // we might be stuck or have reached as close as we can get
            if (!isMoving()) {
                // Check for interruption again
                if (Thread.currentThread().isInterrupted()) {
                    throw new InterruptedException("Thread interrupted while checking if movement stopped");
                }

                // Wait a bit to confirm we've actually stopped
                try {
                    Thread.sleep(500);
                } catch (InterruptedException e) {
                    throw e; // Re-throw to properly stop the script
                }

                // Check for interruption again
                if (Thread.currentThread().isInterrupted()) {
                    throw new InterruptedException("Thread interrupted after confirming movement stopped");
                }

                // Check again if we're still not moving
                if (!isMoving()) {
                    return false;
                }
            }

            // Sleep for a short time before checking again
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                throw e; // Re-throw to properly stop the script
            }
        }

        // If we've reached here, we've timed out
        throw new TimeoutException("Timeout waiting to reach destination: " + destination + ", timeout: " + timeout + "ms");
    }

    // ===== A* PATHFINDING SYSTEM =====

    /**
     * A* pathfinding node for navigation
     */
    private static class PathNode implements Comparable<PathNode> {
        public final Tile tile;
        public final int gCost; // Distance from start
        public final int hCost; // Heuristic distance to goal
        public final int fCost; // Total cost (g + h)
        public final PathNode parent;

        public PathNode(Tile tile, int gCost, int hCost, PathNode parent) {
            this.tile = tile;
            this.gCost = gCost;
            this.hCost = hCost;
            this.fCost = gCost + hCost;
            this.parent = parent;
        }

        @Override
        public int compareTo(PathNode other) {
            return Integer.compare(this.fCost, other.fCost);
        }

        @Override
        public boolean equals(Object obj) {
            if (this == obj) return true;
            if (obj == null || getClass() != obj.getClass()) return false;
            PathNode pathNode = (PathNode) obj;
            return tile.equals(pathNode.tile);
        }

        @Override
        public int hashCode() {
            return tile.hashCode();
        }
    }

    /**
     * Finds a path using A* pathfinding algorithm
     * Handles walls, obstacles, doors, and multi-level navigation
     *
     * @param start The starting tile
     * @param goal The destination tile
     * @param maxDistance Maximum search distance (prevents infinite searches)
     * @return A list of tiles representing the path, or null if no path found
     */
    public static List<Tile> findAStarPath(Tile start, Tile goal, int maxDistance) {
        logger.info("Finding A* path from " + start + " to " + goal);

        if (start.equals(goal)) {
            return Arrays.asList(start);
        }

        PriorityQueue<PathNode> openSet = new PriorityQueue<>();
        Set<Tile> closedSet = new HashSet<>();
        java.util.Map<Tile, PathNode> allNodes = new HashMap<>();

        PathNode startNode = new PathNode(start, 0, manhattanDistance(start, goal), null);
        openSet.add(startNode);
        allNodes.put(start, startNode);

        while (!openSet.isEmpty()) {
            PathNode current = openSet.poll();
            closedSet.add(current.tile);

            // Check if we've reached the goal
            if (current.tile.equals(goal)) {
                return reconstructPath(current);
            }

            // Explore neighbors
            for (Tile neighbor : getWalkableNeighbors(current.tile, goal)) {
                if (closedSet.contains(neighbor)) continue;

                // Skip if too far from start
                if (manhattanDistance(start, neighbor) > maxDistance) continue;

                int tentativeGCost = current.gCost + getMovementCost(current.tile, neighbor);
                PathNode neighborNode = allNodes.get(neighbor);

                if (neighborNode == null || tentativeGCost < neighborNode.gCost) {
                    int hCost = manhattanDistance(neighbor, goal);
                    PathNode newNode = new PathNode(neighbor, tentativeGCost, hCost, current);

                    allNodes.put(neighbor, newNode);
                    openSet.remove(neighborNode); // Remove old node if exists
                    openSet.add(newNode);
                }
            }
        }

        logger.warning("No A* path found from " + start + " to " + goal);
        return null;
    }

    /**
     * Gets walkable neighboring tiles, handling obstacles, doors, and ladders
     */
    private static List<Tile> getWalkableNeighbors(Tile tile, Tile goal) {
        List<Tile> neighbors = new ArrayList<>();

        // Check all 8 directions (including diagonals)
        int[] dx = {-1, -1, -1, 0, 0, 1, 1, 1};
        int[] dy = {-1, 0, 1, -1, 1, -1, 0, 1};

        for (int i = 0; i < 8; i++) {
            Tile neighbor = new Tile(tile.getX() + dx[i], tile.getY() + dy[i], tile.getPlane());

            if (isWalkable(tile, neighbor, goal)) {
                neighbors.add(neighbor);
            }
        }

        // Check for ladders/stairs to other planes
        neighbors.addAll(getMultiLevelConnections(tile, goal));

        return neighbors;
    }

    /**
     * Checks if movement from one tile to another is walkable
     */
    private static boolean isWalkable(Tile from, Tile to, Tile goal) {
        // Basic sanity checking (but not 104x104 bounds since we use global coordinates)
        if (to.getX() < 0 || to.getY() < 0) {
            return false;
        }

        // Same plane movement
        if (from.getPlane() == to.getPlane()) {
            return isLocalWalkable(from, to, goal);
        }

        // Different plane movement (handled by getMultiLevelConnections)
        return false;
    }

    /**
     * Checks if local movement is possible (same plane)
     */
    private static boolean isLocalWalkable(Tile from, Tile to, Tile goal) {
        try {
            // Use game's collision detection
            int fromX = from.getX();
            int fromY = from.getY();
            int toX = to.getX();
            int toY = to.getY();
            int plane = from.getPlane();

            // Check collision map with proper coordinate conversion
            if (plane < PathFinder.collisionMaps.length) {
                CollisionMap collisionMap = PathFinder.collisionMaps[plane];
                if (collisionMap != null) {
                    // Convert global coordinates to local collision map coordinates
                    // The collision map has xOffset and zOffset fields that need to be subtracted
                    // We need to access these through reflection or use the game's coordinate system

                    // Get current camera origin for coordinate conversion
                    int originX = rt4.Camera.originX;
                    int originZ = rt4.Camera.originZ;

                    // Convert to local coordinates relative to current scene
                    int localToX = toX - (originX * 128);
                    int localToY = toY - (originZ * 128);

                    // Ensure coordinates are within collision map bounds (104x104)
                    if (localToX >= 0 && localToX < collisionMap.flags.length &&
                            localToY >= 0 && localToY < collisionMap.flags[0].length) {

                        int collision = collisionMap.flags[localToX][localToY];

                        // Check for walls/obstacles
                        if ((collision & 0x1280100) != 0) { // Blocked tile
                            // Check if there's a door we can open
                            if (isDoorAt(to) && (to.equals(goal) || isNearGoal(to, goal))) {
                                return true; // Can open door if needed
                            }
                            return false;
                        }
                    } else {
                        // Coordinates are outside current collision map region
                        // Use basic heuristics to avoid known problematic areas
                        return isLikelyWalkableOutsideRegion(to, goal);
                    }
                }
            }

            return true;
        } catch (Exception e) {
            logger.warning("Error checking walkability: " + e.getMessage());
            return false;
        }
    }

    /**
     * Finds multi-level connections (ladders, stairs) from a tile
     */
    private static List<Tile> getMultiLevelConnections(Tile tile, Tile goal) {
        List<Tile> connections = new ArrayList<>();

        try {
            // Check for ladders/stairs at this location
            int plane = tile.getPlane();
            int x = tile.getX();
            int y = tile.getY();

            // Convert global coordinates to local coordinates for SceneGraph access
            int originX = rt4.Camera.originX;
            int originZ = rt4.Camera.originZ;
            int localX = x - (originX * 128);
            int localY = y - (originZ * 128);

            // Only check for scenery if coordinates are within bounds
            if (localX >= 0 && localX < 104 && localY >= 0 && localY < 104) {
                Scenery scenery = SceneGraph.getScenery(plane, localX, localY);
                if (scenery != null) {
                    int sceneryId = (int)(scenery.key >> 32) & 0x7fffffff;

                    // Check if it's a ladder or stairs (common IDs)
                    if (isLadderOrStairs(sceneryId)) {
                        // Add connections to adjacent planes
                        if (plane > 0) {
                            connections.add(new Tile(x, y, plane - 1)); // Down
                        }
                        if (plane < 3) {
                            connections.add(new Tile(x, y, plane + 1)); // Up
                        }
                    }
                }
            }
            // If coordinates are out of bounds, just skip - no error needed
        } catch (Exception e) {
            // Log all errors including bounds issues so you can see if there are problems
            String errorMsg = e.getMessage();
            if (errorMsg != null && !errorMsg.isEmpty()) {
                logger.warning("Error finding multi-level connections: " + errorMsg);
            }
        }

        return connections;
    }

    /**
     * Heuristic check for walkability when outside collision map region
     * Uses known problematic coordinates to avoid walls and barriers
     */
    private static boolean isLikelyWalkableOutsideRegion(Tile tile, Tile goal) {
        int x = tile.getX();
        int y = tile.getY();

        // Al Kharid gate/wall area (common problem area)
        // The wall runs roughly from (3264, 3226) to (3264, 3230) and (3267, 3226) to (3267, 3230)
        if (x >= 3264 && x <= 3267 && y >= 3226 && y <= 3230) {
            // Only allow passage through the gate at (3268, 3228)
            return (x == 3268 && y == 3228);
        }

        // Lumbridge castle walls - avoid the castle structure
        if (x >= 3206 && x <= 3222 && y >= 3206 && y <= 3230) {
            // Allow passage around the castle but not through walls
            // This is a simplified check - in reality we'd need more detailed wall data
            return true; // For now, allow movement in this area
        }

        // Varrock walls - major city walls
        if ((x >= 3177 && x <= 3185 && y >= 3376 && y <= 3444) || // West wall
                (x >= 3177 && x <= 3267 && y >= 3444 && y <= 3452) || // North wall
                (x >= 3267 && x <= 3275 && y >= 3376 && y <= 3444) || // East wall
                (x >= 3177 && x <= 3267 && y >= 3376 && y <= 3384)) { // South wall
            // Only allow through known gates
            return isKnownGate(x, y);
        }

        // Default: assume walkable for areas we don't have specific data for
        // This is still imperfect but better than blocking everything
        return true;
    }

    /**
     * Checks if coordinates represent a known gate or entrance
     */
    private static boolean isKnownGate(int x, int y) {
        // Al Kharid gate
        if (x == 3268 && y == 3228) return true;

        // Varrock gates (approximate coordinates)
        if (x == 3181 && y == 3414) return true; // West gate
        if (x == 3237 && y == 3448) return true; // North gate
        if (x == 3271 && y == 3414) return true; // East gate
        if (x == 3237 && y == 3380) return true; // South gate

        return false;
    }

    /**
     * Checks if a scenery ID represents a ladder or stairs
     */
    private static boolean isLadderOrStairs(int sceneryId) {
        // Comprehensive ladder/stair IDs from RuneScape 2009 object list
        int[] ladderIds = {
                // Basic ladders
                10, 11, 60, 101, 132, 133, 195, 245, 246, 272, 273, 287, 743,

                // Stairs
                54, 55, 56, 57, 96, 98,

                // Ship ladders
                245, 246, 272, 273,

                // Player-owned house stairs
                13497, 13498, 13499, 13500, 13501, 13502, 13503, 13504, 13505, 13506,

                // Common ladder object IDs
                1746, 1747, 1748, 1749, 1750, 1751, 1752, 1753, 1754,

                // Stair object IDs
                1755, 1756, 1757, 1758, 1759, 1760, 1761, 1762, 1763,

                // Stone stairs
                2147, 2148, 2149, 2150, 2151, 2152, 2153, 2154, 2155,

                // Wooden stairs
                4493, 4494, 4495, 4496, 4497, 4498, 4499, 4500, 4501,

                // Spiral stairs
                1764, 1765, 1766, 1767, 1768, 1769, 1770, 1771, 1772,

                // Cave ladders
                1773, 1774, 1775, 1776, 1777, 1778, 1779, 1780, 1781,

                // Dungeon stairs
                1782, 1783, 1784, 1785, 1786, 1787, 1788, 1789, 1790,

                // Magic stairs (teleport stairs)
                1791, 1792, 1793, 1794, 1795, 1796, 1797, 1798, 1799,

                // Rope ladders
                1800, 1801, 1802, 1803, 1804, 1805, 1806, 1807, 1808
        };

        for (int id : ladderIds) {
            if (sceneryId == id) return true;
        }

        return false;
    }

    /**
     * Checks if there's a door at the specified tile
     */
    private static boolean isDoorAt(Tile tile) {
        try {
            int plane = tile.getPlane();
            int x = tile.getX();
            int y = tile.getY();

            Scenery scenery = SceneGraph.getScenery(plane, x, y);
            if (scenery != null) {
                int sceneryId = (int)(scenery.key >> 32) & 0x7fffffff;
                return isDoor(sceneryId);
            }
        } catch (Exception e) {
            logger.warning("Error checking for door: " + e.getMessage());
        }

        return false;
    }

    /**
     * Checks if a scenery ID represents a door
     */
    private static boolean isDoor(int sceneryId) {
        // Comprehensive door IDs from RuneScape 2009 object list
        int[] doorIds = {
                // Basic doors
                3, 4, 22, 24, 59, 71, 72, 73, 74, 77, 78, 79, 80, 81, 82, 92, 93, 99, 102, 131, 134, 135, 136,

                // Large doors
                71, 72, 73, 74, 134, 135,

                // Prison doors
                79, 80,

                // Bamboo doors
                779,

                // City gates (large doors)
                788, 789,

                // Dungeon doors (Player-owned house)
                13344, 13345, 13346, 13347, 13348, 13349, 13350, 13351, 13352, 13353, 13354, 13355,

                // Confusing doors (quest-related)
                13913, 13914, 13915, 13916, 13917, 13918, 13919, 13920, 13921, 13922, 13923, 13924,

                // Gates (door-like)
                37, 38, 39, 47, 48, 49, 50, 52, 53, 89, 90, 94, 95, 166, 167, 190,

                // Trapdoors (door-like)
                100, 105, 106, 13675, 13676, 13677, 13678, 13679, 13680,

                // Additional door variations
                1516, 1517, 1518, 1519, // Basic wooden doors
                1530, 1531, 1532, 1533, // Wooden doors
                2112, 2113, 2114, 2115, // Metal doors
                2676, 2677, // Mine door entrance

                // Common door object IDs found in various locations
                1519, 1520, 1521, 1522, 1523, 1524, 1525, 1526, 1527, 1528, 1529,
                1533, 1534, 1535, 1536, 1537, 1538, 1539, 1540, 1541, 1542, 1543,

                // Bank doors and secure doors
                2213, 2214, 2215, 2216, 2217, 2218, 2219, 2220,

                // Quest-specific doors
                2255, // Ancient metal gate

                // House doors
                11402, 11403, 11404, 11405, 11406, 11407, 11408, 11409
        };

        for (int id : doorIds) {
            if (sceneryId == id) return true;
        }

        return false;
    }

    /**
     * Checks if a tile is near the goal (within 1 tile)
     */
    private static boolean isNearGoal(Tile tile, Tile goal) {
        return Math.abs(tile.getX() - goal.getX()) <= 1 &&
                Math.abs(tile.getY() - goal.getY()) <= 1 &&
                tile.getPlane() == goal.getPlane();
    }

    /**
     * Calculates Manhattan distance between two tiles
     */
    private static int manhattanDistance(Tile a, Tile b) {
        int dx = Math.abs(a.getX() - b.getX());
        int dy = Math.abs(a.getY() - b.getY());
        int dz = Math.abs(a.getPlane() - b.getPlane()) * 10; // Plane changes are expensive
        return dx + dy + dz;
    }

    /**
     * Gets movement cost between two adjacent tiles
     */
    private static int getMovementCost(Tile from, Tile to) {
        // Diagonal movement costs more
        boolean diagonal = Math.abs(from.getX() - to.getX()) == 1 &&
                Math.abs(from.getY() - to.getY()) == 1;

        // Plane changes are expensive
        boolean planeChange = from.getPlane() != to.getPlane();

        if (planeChange) return 50; // High cost for using ladders/stairs
        if (diagonal) return 14;    // √2 * 10 for diagonal movement
        return 10;                  // Standard cost for orthogonal movement
    }

    /**
     * Reconstructs the path from the goal node back to the start
     */
    private static List<Tile> reconstructPath(PathNode goalNode) {
        List<Tile> path = new ArrayList<>();
        PathNode current = goalNode;

        while (current != null) {
            path.add(current.tile);
            current = current.parent;
        }

        Collections.reverse(path);
        return path;
    }

    /**
     * Optimizes a path for minimap walking by downsampling to waypoints 5-8 tiles apart
     * This reduces the number of clicks needed and makes pathfinding more efficient
     */
    private static List<Tile> optimizePathForMinimap(List<Tile> originalPath) {
        if (originalPath == null || originalPath.size() <= 1) {
            return originalPath;
        }

        List<Tile> optimizedPath = new ArrayList<>();
        optimizedPath.add(originalPath.get(0)); // Always include the start

        Tile lastWaypoint = originalPath.get(0);
        int targetDistance = 6; // Target 6 tiles apart (middle of 5-8 range)

        for (int i = 1; i < originalPath.size(); i++) {
            Tile currentTile = originalPath.get(i);
            double distance = lastWaypoint.distanceTo(currentTile);

            // Add waypoint if we've reached target distance or this is the last tile
            if (distance >= targetDistance || i == originalPath.size() - 1) {
                optimizedPath.add(currentTile);
                lastWaypoint = currentTile;
            }
        }

        logger.info("Path optimization: " + originalPath.size() + " tiles -> " + optimizedPath.size() + " waypoints (~" + targetDistance + " tiles apart)");
        return optimizedPath;
    }

    /**
     * Regional A* pathfinding that works within 104x104 tile collision map constraints.
     * This method breaks long-distance paths into regional chunks and recalculates
     * the path as the player moves between regions.
     */
    private static List<Tile> findRegionalAStarPath(Tile start, Tile destination) {
        logger.info("Starting regional A* pathfinding from " + start + " to " + destination);

        List<Tile> fullPath = new ArrayList<>();
        Tile currentStart = start;

        // Maximum distance we can pathfind within one region (conservative estimate)
        final int MAX_REGIONAL_DISTANCE = 80; // Stay well within 104x104 bounds

        int maxIterations = 20; // Prevent infinite loops
        int iteration = 0;

        while (!currentStart.equals(destination) && iteration < maxIterations) {
            iteration++;

            double remainingDistance = currentStart.distanceTo(destination);
            logger.info("Regional pathfinding iteration " + iteration + ", remaining distance: " + remainingDistance);

            // Determine the target for this regional segment
            Tile regionalTarget;
            if (remainingDistance <= MAX_REGIONAL_DISTANCE) {
                // Final segment - go directly to destination
                regionalTarget = destination;
            } else {
                // Intermediate segment - find a point towards the destination
                regionalTarget = calculateIntermediateTarget(currentStart, destination, MAX_REGIONAL_DISTANCE);
            }

            // Find path within this region using standard A*
            List<Tile> regionalPath = findAStarPath(currentStart, regionalTarget, MAX_REGIONAL_DISTANCE + 20);

            if (regionalPath == null || regionalPath.isEmpty()) {
                logger.warning("Regional A* failed for segment " + iteration + " from " + currentStart + " to " + regionalTarget);

                // Fallback: create a direct line segment
                regionalPath = createDirectLineSegment(currentStart, regionalTarget, MAX_REGIONAL_DISTANCE);
            }

            // Add this segment to the full path (skip first tile if not the first segment to avoid duplicates)
            if (iteration == 1) {
                fullPath.addAll(regionalPath);
            } else {
                fullPath.addAll(regionalPath.subList(1, regionalPath.size()));
            }

            // Update start position for next iteration
            currentStart = regionalPath.get(regionalPath.size() - 1);

            // Check if we've reached the destination
            if (currentStart.distanceTo(destination) < 2.0) {
                break;
            }
        }

        if (iteration >= maxIterations) {
            logger.warning("Regional pathfinding reached maximum iterations, path may be incomplete");
        }

        logger.info("Regional pathfinding completed in " + iteration + " iterations, total path length: " + fullPath.size());
        return optimizePathForMinimap(fullPath);
    }

    /**
     * Calculates an intermediate target point towards the destination within the specified distance
     */
    private static Tile calculateIntermediateTarget(Tile start, Tile destination, int maxDistance) {
        double totalDistance = start.distanceTo(destination);
        double ratio = Math.min(maxDistance / totalDistance, 1.0);

        int targetX = (int) (start.getX() + ratio * (destination.getX() - start.getX()));
        int targetY = (int) (start.getY() + ratio * (destination.getY() - start.getY()));
        int targetZ = start.getPlane(); // Keep same plane for intermediate targets

        return new Tile(targetX, targetY, targetZ);
    }

    /**
     * Creates a direct line segment as a fallback when A* pathfinding fails
     */
    private static List<Tile> createDirectLineSegment(Tile start, Tile end, int maxLength) {
        List<Tile> segment = new ArrayList<>();

        double distance = start.distanceTo(end);
        int steps = Math.min((int) Math.ceil(distance), maxLength);

        for (int i = 0; i <= steps; i++) {
            double ratio = (double) i / steps;
            int x = (int) (start.getX() + ratio * (end.getX() - start.getX()));
            int y = (int) (start.getY() + ratio * (end.getY() - start.getY()));
            int z = start.getPlane();

            segment.add(new Tile(x, y, z));
        }

        return segment;
    }

    /**
     * Enhanced walkToTile method using A* pathfinding for long distances
     */
    public static boolean walkToTileWithPathfinding(Tile destination) throws InterruptedException, TimeoutException {
        return walkToTileWithPathfinding(destination, new TimeoutConfig());
    }

    /**
     * Enhanced walkToTile method using A* pathfinding for long distances
     */
    public static boolean walkToTileWithPathfinding(Tile destination, TimeoutConfig timeoutConfig) throws InterruptedException, TimeoutException {
        // ALWAYS use global coordinates for pathfinding
        Tile start = getPlayerGlobalLocation();
        if (start == null) {
            logger.warning("Cannot get player global location for pathfinding");
            return false;
        }

        // Ensure destination is also in global coordinates
        Tile globalDestination = destination;
        if (destination.getX() < 1000 && destination.getY() < 1000) {
            // Convert local destination to global
            globalDestination = destination.asGlobal();
            logger.info("Converted destination from local to global: " + destination + " -> " + globalDestination);
        }

        double distance = start.distanceTo(globalDestination);

        // For short distances, use existing local pathfinding
        if (distance < 20) {
            return walkToTile(globalDestination);
        }

        // For long distances, use regional A* pathfinding
        logger.info("Using regional A* pathfinding for long distance: " + distance + " tiles");
        logger.info("Start (global): " + start + ", Destination (global): " + globalDestination);

        List<Tile> path = findRegionalAStarPath(start, globalDestination);

        if (path == null || path.isEmpty()) {
            logger.warning("Regional A* pathfinding failed, falling back to direct walk");
            return walkToTile(globalDestination);
        }

        // Break down the path into smaller waypoints (max 5 tiles apart) for minimap walking
        List<Tile> optimizedPath = optimizePathForMinimap(path);

        // Walk the optimized path
        return walkPath(optimizedPath.toArray(new Tile[0]), timeoutConfig);
    }

}