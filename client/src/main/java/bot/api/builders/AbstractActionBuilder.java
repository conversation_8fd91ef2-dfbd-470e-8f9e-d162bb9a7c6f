package bot.api.builders;

import bot.api.interaction.WalkInteraction;
import bot.util.error.TimeoutConfig;
import bot.util.error.TimeoutHandler;
import bot.util.logging.Logger;
import bot.util.logging.LoggerFactory;
import bot.util.game.world.Tile;

import java.util.Random;
import java.util.concurrent.TimeoutException;
import java.util.function.Consumer;

/**
 * Abstract base implementation of ActionBuilder that handles common functionality
 * like delays, retries, and callbacks.
 *
 * @param <T> The implementing builder type (for method chaining)
 */
public abstract class AbstractActionBuilder<T extends ActionBuilder<T>> implements ActionBuilder<T> {

    protected final Logger logger = LoggerFactory.getLogger(getClass().getSimpleName());
    protected final Random random = new Random();

    // Common configuration parameters
    protected int minDelay = 300;
    protected int maxDelay = 600;
    protected int retryAttempts = 1;
    protected Runnable successCallback = null;
    protected Runnable failureCallback = null;
    protected Consumer<TimeoutException> timeoutCallback = null;

    // Common walking parameters
    protected int maxWalkDistance = 20;

    // Timeout configuration
    protected TimeoutConfig timeoutConfig = TimeoutHandler.getDefaultConfig();

    /**
     * Gets this instance cast to the implementing type for method chaining.
     */
    @SuppressWarnings("unchecked")
    protected T self() {
        return (T) this;
    }

    @Override
    public T withDelay(int minMs, int maxMs) {
        this.minDelay = minMs;
        this.maxDelay = maxMs;
        return self();
    }

    @Override
    public T withRetries(int attempts) {
        this.retryAttempts = Math.max(1, attempts);
        return self();
    }

    /**
     * Sets the maximum distance to walk to the target entity.
     *
     * @param tiles The maximum distance in tiles
     * @return this builder for method chaining
     */
    public T withMaxWalkDistance(int tiles) {
        this.maxWalkDistance = tiles;
        return self();
    }

    /**
     * Sets the timeout for walking operations.
     *
     * @param milliseconds The timeout in milliseconds
     * @return this builder for method chaining
     */
    public T withWalkTimeout(int milliseconds) {
        this.timeoutConfig = new TimeoutConfig(this.timeoutConfig)
                .withWalkTimeout(milliseconds);
        return self();
    }

    /**
     * Sets the timeout configuration for this builder.
     * This will override all previously set timeout values.
     *
     * @param config The TimeoutConfig to use
     * @return this builder for method chaining
     */
    public T withTimeoutConfig(TimeoutConfig config) {
        if (config == null) {
            throw new IllegalArgumentException("TimeoutConfig cannot be null");
        }
        this.timeoutConfig = config;
        return self();
    }

    @Override
    public T onSuccess(Runnable callback) {
        this.successCallback = callback;
        return self();
    }

    @Override
    public T onFailure(Runnable callback) {
        this.failureCallback = callback;
        return self();
    }

    @Override
    public boolean execute() throws InterruptedException, TimeoutException {
        for (int attempt = 0; attempt < retryAttempts; attempt++) {
            try {
                if (attempt > 0) {
                    logger.info("Retry attempt " + attempt + " of " + retryAttempts);
                    sleep(minDelay, maxDelay);
                }

                boolean result = executeAction();

                if (result) {
                    if (successCallback != null) {
                        successCallback.run();
                    }
                    return true;
                }

                // If we get here, the action failed but didn't throw an exception
                logger.info("Action failed, will " +
                           (attempt < retryAttempts - 1 ? "retry" : "abort"));

            } catch (InterruptedException e) {
                // Propagate InterruptedException to allow proper script pausing/stopping
                logger.info("Action interrupted, propagating interruption");
                throw e;
            } catch (TimeoutException e) {
                // Handle timeout exceptions specifically
                logger.warning("Timeout during action execution: " + e.getMessage());
                // Don't retry on timeout as it's likely to timeout again
                if (failureCallback != null) {
                    failureCallback.run();
                }
                return false;
            } catch (Exception e) {
                logger.warning("Exception during action execution: " + e.getMessage());
            }
        }

        // If we get here, all attempts failed
        if (failureCallback != null) {
            failureCallback.run();
        }
        return false;
    }

    /**
     * Executes the specific action implementation.
     * This method should be implemented by concrete builder classes.
     *
     * @return true if the action was successful, false otherwise
     * @throws InterruptedException if the thread is interrupted, which should be allowed to propagate
     *                              to properly handle script pausing/stopping
     * @throws TimeoutException if the operation times out
     */
    protected abstract boolean executeAction() throws InterruptedException, TimeoutException;

    /**
     * Sleeps for a random duration between the specified min and max values.
     *
     * @param minMs minimum sleep time in milliseconds
     * @param maxMs maximum sleep time in milliseconds
     * @throws InterruptedException if the thread is interrupted, which should be allowed to propagate
     *                              to properly handle script pausing/stopping
     */
    protected void sleep(int minMs, int maxMs) throws InterruptedException {
        Thread.sleep(random.nextInt(maxMs - minMs + 1) + minMs);
    }

    /**
     * Checks if a target location is within the maximum walk distance.
     *
     * @param targetTile The target tile to check
     * @return true if the target is within the maximum walk distance, false otherwise
     */
    protected boolean isWithinWalkDistance(Tile targetTile) {
        Tile playerTile = WalkInteraction.getPlayerLocation();
        double distance = playerTile.distanceTo(targetTile);

        if (distance > maxWalkDistance) {
            logger.warning("Target is too far away: " + distance + " tiles (max: " + maxWalkDistance + ")");
            return false;
        }

        return true;
    }

    /**
     * Walks to a target location with the configured timeout.
     *
     * @param targetTile The target tile to walk to
     * @return true if successfully walked to the target, false otherwise
     * @throws InterruptedException if the thread is interrupted, which should be allowed to propagate
     *                              to properly handle script pausing/stopping
     * @throws TimeoutException if the operation times out
     */
    protected boolean walkToTarget(Tile targetTile) throws InterruptedException, TimeoutException {
        logger.info("Walking to target at " + targetTile);
        return WalkInteraction.walkToLocal(targetTile);
    }
}
