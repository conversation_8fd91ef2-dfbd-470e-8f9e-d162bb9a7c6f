package bot.api.builders;

import bot.core.ScriptManager;
import bot.api.interaction.WalkInteraction;
import bot.util.game.AnimationUtils;
import bot.util.game.world.Tile;
import bot.util.logging.Logger;
import bot.util.logging.LoggerFactory;
import bot.util.error.TimeoutConfig;
import bot.util.error.TimeoutHandler;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeoutException;
import java.util.function.Predicate;

/**
 * A clean, focused builder for walking operations in the task chaining framework.
 * Provides a chainable API for common walking tasks.
 */
public class WalkingBuilder extends AbstractActionBuilder<WalkingBuilder> {

    private static final Logger logger = LoggerFactory.getLogger(WalkingBuilder.class.getSimpleName());

    // Walking configuration
    private Tile destination = null;
    private Tile[] path = null;
    private int proximityDistance = 3; // How close to get to the destination

    // Path randomization
    private boolean randomizePath = false;
    private int randomizeX = 0;
    private int randomizeY = 0;

    // Destination validation
    private Predicate<Tile> destinationValidator = null;
    private boolean waitUntilNotMoving = true;

    /**
     * Creates a new WalkingBuilder without a specific destination.
     */
    public WalkingBuilder() {
        // Default constructor
    }

    /**
     * Creates a new WalkingBuilder with a specific destination tile.
     *
     * @param destination the destination tile to walk to
     */
    public WalkingBuilder(Tile destination) {
        this.destination = destination;
    }



    /**
     * Sets the destination tile to walk to.
     *
     * @param destination the destination tile
     * @return this builder for method chaining
     */
    public WalkingBuilder to(Tile destination) {
        this.destination = destination;
        return this;
    }



    /**
     * Sets the destination tile to walk to.
     *
     * @param x the x-coordinate
     * @param y the y-coordinate
     * @param plane the plane (0 = ground level)
     * @return this builder for method chaining
     */
    public WalkingBuilder to(int x, int y, int plane) {
        this.destination = new Tile(x, y, plane);
        return this;
    }

    /**
     * Sets the destination tile to walk to on the ground level.
     *
     * @param x the x-coordinate
     * @param y the y-coordinate
     * @return this builder for method chaining
     */
    public WalkingBuilder to(int x, int y) {
        return to(x, y, 0);
    }

    /**
     * Sets a path of tiles to walk along.
     *
     * @param path the path of tiles to walk
     * @return this builder for method chaining
     */
    public WalkingBuilder alongPath(Tile[] path) {
        this.path = path;
        return this;
    }



    /**
     * Sets a path of tiles to walk along.
     *
     * @param tiles a list of tiles to walk
     * @return this builder for method chaining
     */
    public WalkingBuilder alongPath(List<Tile> tiles) {
        this.path = tiles.toArray(new Tile[0]);
        return this;
    }



    /**
     * Sets the maximum timeout for the entire walking operation.
     *
     * @param milliseconds the timeout in milliseconds
     * @return this builder for method chaining
     */
    public WalkingBuilder withTimeout(int milliseconds) {
        this.timeoutConfig = new TimeoutConfig(this.timeoutConfig)
                .withWalkTimeout(milliseconds);
        return this;
    }

    /**
     * Sets the timeout for reaching each tile in a path.
     * This is stored in the TimeoutConfig as the default timeout.
     *
     * @param milliseconds the timeout in milliseconds
     * @return this builder for method chaining
     */
    public WalkingBuilder withTileTimeout(int milliseconds) {
        this.timeoutConfig = new TimeoutConfig(this.timeoutConfig)
                .withDefaultTimeout(milliseconds);
        return this;
    }

    /**
     * Sets how close to get to the destination.
     *
     * @param tiles the distance in tiles
     * @return this builder for method chaining
     */
    public WalkingBuilder withProximity(int tiles) {
        this.proximityDistance = tiles;
        return this;
    }

    /**
     * Configures the builder to randomize the path.
     *
     * @param maxXDeviation the maximum x deviation
     * @param maxYDeviation the maximum y deviation
     * @return this builder for method chaining
     */
    public WalkingBuilder randomize(int maxXDeviation, int maxYDeviation) {
        this.randomizePath = true;
        this.randomizeX = maxXDeviation;
        this.randomizeY = maxYDeviation;
        return this;
    }

    /**
     * Configures the builder to validate the destination before walking.
     *
     * @param validator a predicate that returns true if the destination is valid
     * @return this builder for method chaining
     */
    public WalkingBuilder validateDestination(Predicate<Tile> validator) {
        this.destinationValidator = validator;
        return this;
    }

    /**
     * Configures the builder to wait until the player stops moving.
     *
     * @param wait true to wait, false to continue immediately
     * @return this builder for method chaining
     */
    public WalkingBuilder waitUntilNotMoving(boolean wait) {
        this.waitUntilNotMoving = wait;
        return this;
    }

    @Override
    protected boolean executeAction() throws InterruptedException, TimeoutException {
        // Validate configuration
        if (destination == null && path == null) {
            logger.warning("No destination or path specified");
            return false;
        }

        // If we have a destination validator, check if the destination is valid
        if (destination != null && destinationValidator != null && !destinationValidator.test(destination)) {
            logger.warning("Destination failed validation check");
            return false;
        }

        // If we have a single destination, convert it to a path
        if (destination != null && path == null) {
            path = new Tile[] { destination };
        }

        // Randomize the path if configured
        Tile[] walkingPath = path;
        if (randomizePath && randomizeX > 0 && randomizeY > 0) {
            walkingPath = randomizePath(path, randomizeX, randomizeY);
        }

        // Walk the path
        logger.info("Walking path with " + walkingPath.length + " waypoints");
        try {
            boolean walkResult = WalkInteraction.walkPath(walkingPath);

            if (!walkResult) {
                logger.warning("Failed to walk path");
                return false;
            }
        } catch (TimeoutException e) {
            logger.warning("Timeout while walking path: " + e.getMessage());
            throw e; // Propagate the timeout exception to be handled by AbstractActionBuilder
        }

        // Wait until the player stops moving if configured
        if (waitUntilNotMoving) {
            logger.info("Waiting for player to stop moving");
            waitUntilNotMoving();
        }

        // Add natural delay after walking
        sleep(minDelay, maxDelay);

        // Check if we reached the destination
        Tile playerLoc = WalkInteraction.getPlayerGlobalLocation();
        Tile finalDestination = destination != null ? destination : path[path.length - 1];

        // Ensure finalDestination is in global coordinates
        if (finalDestination.getX() < 1000 && finalDestination.getY() < 1000) {
            finalDestination = finalDestination.asGlobal();
        }

        // Calculate distance using the Tile.distanceTo method
        double distance = playerLoc.distanceTo(finalDestination);

        if (distance <= proximityDistance) {
            logger.info("Successfully reached destination (distance: " + String.format("%.1f", distance) + " tiles)");
            return true;
        } else {
            logger.warning("Failed to reach destination, still " + String.format("%.1f", distance) + " tiles away");
            return false;
        }
    }

    /**
     * Randomizes a path of tiles.
     *
     * @param path the path to randomize
     * @param maxXDeviation the maximum x deviation
     * @param maxYDeviation the maximum y deviation
     * @return the randomized path
     */
    private Tile[] randomizePath(Tile[] path, int maxXDeviation, int maxYDeviation) {
        List<Tile> randomized = new ArrayList<>();

        for (Tile tile : path) {
            // Generate random offsets
            int xOffset = random.nextInt(maxXDeviation * 2 + 1) - maxXDeviation;
            int yOffset = random.nextInt(maxYDeviation * 2 + 1) - maxYDeviation;

            // Create a new tile with the offsets
            randomized.add(new Tile(
                tile.getX() + xOffset,
                tile.getY() + yOffset,
                tile.getPlane()
            ));
        }

        return randomized.toArray(new Tile[0]);
    }

    /**
     * Waits until the player stops moving.
     *
     * @throws InterruptedException if the thread is interrupted
     */
    private void waitUntilNotMoving() throws InterruptedException {
        long startTime = System.currentTimeMillis();

        while (AnimationUtils.isMoving()) {
            // Check if script is paused
            if (ScriptManager.getInstance().isScriptPaused()) {
                logger.debug("Script paused while waiting for player to stop moving");
                // Wait until the script is resumed or interrupted
                while (ScriptManager.getInstance().isScriptPaused() && !Thread.currentThread().isInterrupted()) {
                    Thread.sleep(50);
                }
                // If the thread was interrupted while paused, throw an exception
                if (Thread.currentThread().isInterrupted()) {
                    logger.debug("Thread interrupted while paused waiting for player to stop moving");
                    throw new InterruptedException("Thread interrupted while paused waiting for player to stop moving");
                }
                // Reset the start time to account for the pause
                long pauseDuration = System.currentTimeMillis() - startTime;
                startTime = System.currentTimeMillis() - pauseDuration;
            }

            // Check if thread has been interrupted
            if (Thread.currentThread().isInterrupted()) {
                logger.debug("Thread interrupted while waiting for player to stop moving");
                throw new InterruptedException("Thread interrupted while waiting for player to stop moving");
            }

            // Check if we've timed out
            if (System.currentTimeMillis() - startTime > timeoutConfig.getWalkTimeout()) {
                logger.warning("Timed out waiting for player to stop moving");
                return;
            }

            // Sleep for a short time to avoid busy waiting
            Thread.sleep(100);
        }
    }
}
